<template>
  <el-dialog
    v-model="visible"
    title="多肉等级详情"
    width="900px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="等级">
          {{ data.level }}级
        </el-descriptions-item>
        <el-descriptions-item label="等级名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="图标">
          <img
            :src="data.icon"
            :alt="data.name"
            style="
              width: 32px;
              height: 32px;
              object-fit: cover;
              border-radius: 4px;
            "
          />
        </el-descriptions-item>
        <el-descriptions-item label="排序">
          {{ data.sort_order }}
        </el-descriptions-item>
        <el-descriptions-item label="所需能量">
          {{ data.required_energy }}
        </el-descriptions-item>
        <el-descriptions-item label="金币奖励">
          {{ data.coin_reward }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.is_active ? 'success' : 'danger'">
            {{ data.is_active ? "启用" : "禁用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatTime(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ formatTime(data.updated_at) }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from "vue";
import type { SucculentLevel } from "@/api/succulent-levels";

interface Props {
  modelValue: boolean;
  data?: SucculentLevel | null;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

const handleClose = () => {
  visible.value = false;
};

// 格式化时间
const formatTime = (time?: string) => {
  return time ? new Date(time).toLocaleString() : "-";
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.bonus-stats {
  .bonus-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .bonus-label {
      width: 80px;
      font-weight: 500;
      margin-right: 12px;
    }

    :deep(.el-progress) {
      flex: 1;
      margin-right: 12px;
    }

    .bonus-value {
      font-weight: 600;
      color: var(--el-color-primary);
      min-width: 40px;
    }
  }
}

.abilities {
  line-height: 1.8;
}

.rewards {
  .reward-section {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 16px;

    .reward-item {
      display: flex;
      align-items: center;
      gap: 8px;

      .reward-label {
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }
  }

  .reward-items {
    .reward-label {
      font-weight: 500;
      color: var(--el-text-color-regular);
      margin-bottom: 8px;
      display: block;
    }
  }
}
</style>
