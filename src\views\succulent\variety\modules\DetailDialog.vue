<template>
  <el-dialog
    v-model="visible"
    title="品种详情"
    width="800px"
    draggable
    @close="handleClose"
  >
    <div v-if="data" class="detail-content">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="内部名称">
          {{ data.name }}
        </el-descriptions-item>
        <el-descriptions-item label="显示名称">
          {{ data.display_name }}
        </el-descriptions-item>
        <el-descriptions-item label="稀有度">
          <el-tag
            :type="
              data.rarity === 'rare'
                ? 'success'
                : data.rarity === 'epic'
                  ? 'warning'
                  : data.rarity === 'legendary'
                    ? 'danger'
                    : 'info'
            "
          >
            {{ getRarityLabel(data.rarity) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="解锁等级">
          {{ data.unlock_level }}级
        </el-descriptions-item>
        <el-descriptions-item label="需要VIP">
          <el-tag :type="data.need_vip ? 'warning' : 'info'">
            {{ data.need_vip ? "需要VIP" : "无需VIP" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="data.is_active ? 'success' : 'danger'">
            {{ data.is_active ? "启用" : "禁用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(data.created_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">
          {{ data.description }}
        </el-descriptions-item>
        <el-descriptions-item label="成长阶段" :span="2">
          <div class="growth-stages">
            <el-tag
              v-for="stage in data.growth_stages.stages"
              :key="stage.level"
              class="mr-2 mb-2"
              type="info"
            >
              {{ stage.level }}级 - {{ stage.name }}
            </el-tag>
          </div>
        </el-descriptions-item>
        <el-descriptions-item
          label="图片"
          :span="2"
          v-if="data.image_urls && Object.keys(data.image_urls).length > 0"
        >
          <div class="image-gallery">
            <el-image
              v-for="(url, key) in data.image_urls"
              :key="key"
              :src="url"
              fit="cover"
              style="
                width: 120px;
                height: 120px;
                border-radius: 8px;
                margin-right: 8px;
              "
              :preview-src-list="Object.values(data.image_urls)"
            />
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { getPlantSpeciesDetail, type PlantSpecies } from "@/api/plants";

interface Props {
  modelValue: boolean;
  speciesId?: string;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const data = ref<PlantSpecies | null>(null);

// 获取品种详情数据
const fetchSpeciesDetail = async () => {
  if (!props.speciesId) return;

  try {
    loading.value = true;
    const response = await getPlantSpeciesDetail(props.speciesId);
    if (response.code === 200) {
      data.value = response.data;
    } else {
      ElMessage.error(response.message || "获取品种详情失败");
    }
  } catch (error) {
    console.error("获取品种详情失败:", error);
    ElMessage.error("获取品种详情失败");
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val && props.speciesId) {
      fetchSpeciesDetail();
    }
  },
  { immediate: true }
);

watch(visible, val => {
  emit("update:modelValue", val);
  if (!val) {
    data.value = null;
  }
});

const handleClose = () => {
  visible.value = false;
};

// 获取稀有度标签
const getRarityLabel = (rarity: string) => {
  const rarityMap = {
    common: "普通",
    rare: "稀有",
    epic: "史诗",
    legendary: "传说"
  };
  return rarityMap[rarity as keyof typeof rarityMap] || rarity;
};

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};
</script>

<style scoped lang="scss">
.detail-content {
  padding: 10px 0;
}

.attributes {
  .attribute-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .attribute-label {
      width: 60px;
      font-weight: 500;
      margin-right: 12px;
    }

    :deep(.el-progress) {
      flex: 1;
    }
  }
}
</style>
