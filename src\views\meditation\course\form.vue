<template>
  <div class="main">
    <el-card>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-semibold">
            {{ isEdit ? "编辑课程" : "创建课程" }}
          </span>
          <el-button @click="$router.go(-1)" :icon="useRenderIcon('ep:back')">
            返回课程列表
          </el-button>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="courseForm"
        :rules="formRules"
        label-width="120px"
        class="max-w-2xl"
      >
        <el-form-item label="课程标题" prop="title">
          <el-input
            v-model="courseForm.title"
            placeholder="请输入课程标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="课程描述" prop="description">
          <el-input
            v-model="courseForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入课程描述"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="课程类型" prop="type">
          <el-select
            v-model="courseForm.type"
            placeholder="请选择课程类型"
            style="width: 100%"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="封面图片">
          <el-upload
            ref="coverUploadRef"
            class="cover-uploader"
            :action="uploadAction"
            :headers="uploadHeaders"
            :data="{ folder: 'meditation/courses/covers', fileType: 'image' }"
            :show-file-list="false"
            :on-success="handleCoverSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeCoverUpload"
            :loading="coverUploading"
          >
            <img
              v-if="courseForm.cover_url"
              :src="courseForm.cover_url"
              class="cover-image"
            />
            <el-icon v-else class="cover-uploader-icon">
              <Plus />
            </el-icon>
            <div v-if="coverUploading" class="upload-loading">
              <el-icon class="is-loading">
                <Loading />
              </el-icon>
              <div>上传中...</div>
            </div>
          </el-upload>
          <div class="upload-tip">
            支持 jpg/jpeg/png/gif/webp 格式，大小不超过 5MB
          </div>
        </el-form-item>

        <el-form-item label="课程标签">
          <el-select
            v-model="courseForm.tag_ids"
            multiple
            placeholder="请选择课程标签"
            style="width: 100%"
          >
            <el-option
              v-for="tag in allTags"
              :key="tag.id"
              :label="tag.name"
              :value="parseInt(tag.id)"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="课程状态" prop="status">
          <el-radio-group v-model="courseForm.status">
            <el-radio value="draft">草稿</el-radio>
            <el-radio value="published">发布</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSave" :loading="saving">
            {{ isEdit ? "更新课程" : "创建课程" }}
          </el-button>
          <el-button @click="$router.go(-1)">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage, type FormInstance, type UploadProps } from "element-plus";
import { Plus, Loading } from "@element-plus/icons-vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  createMeditationContent,
  updateMeditationContent,
  getMeditationContentDetail,
  getMeditationTagList,
  type CreateMeditationContentParams,
  type UpdateMeditationContentParams,
  type MeditationTag
} from "@/api/meditation";
import { type UploadResponse } from "@/api/upload";
import { getToken } from "@/utils/auth";

defineOptions({
  name: "CourseForm"
});

const route = useRoute();
const router = useRouter();
const courseId = route.params.id as string;
const isEdit = !!courseId;

const loading = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();
const allTags = ref<MeditationTag[]>([]);

// 上传相关状态
const coverUploading = ref(false);

// 上传配置
const uploadAction = computed(() => {
  return import.meta.env.VITE_BASE_API + "/api/oss/upload";
});

const uploadHeaders = computed(() => {
  const token = getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const courseForm = reactive<CreateMeditationContentParams>({
  title: "",
  description: "",
  type: "meditation",
  sub_type: "course",
  cover_url: "",
  duration: 0,
  tag_ids: [],
  status: "draft"
});

const formRules = {
  title: [{ required: true, message: "请输入课程标题", trigger: "blur" }],
  description: [{ required: true, message: "请输入课程描述", trigger: "blur" }],
  type: [{ required: true, message: "请选择课程类型", trigger: "change" }],
  status: [{ required: true, message: "请选择课程状态", trigger: "change" }]
};

const typeOptions = [
  { label: "冥想", value: "meditation" },
  { label: "声音", value: "sound" },
  { label: "睡眠", value: "sleep" }
];

// 封面图片上传
const beforeCoverUpload: UploadProps["beforeUpload"] = file => {
  const isImage = file.type.startsWith("image/");
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt5M) {
    ElMessage.error("图片大小不能超过 5MB!");
    return false;
  }
  coverUploading.value = true;
  return true;
};

const handleCoverSuccess = (response: UploadResponse) => {
  coverUploading.value = false;
  if (response.code === 200) {
    courseForm.cover_url = response.data.url;
    ElMessage.success("封面上传成功");
  } else {
    ElMessage.error(response.message || "封面上传失败");
  }
};

// 通用上传错误处理
const handleUploadError = (error: any) => {
  coverUploading.value = false;
  console.error("上传失败:", error);
  ElMessage.error("文件上传失败，请重试");
};

// 加载标签列表
const loadTags = async () => {
  try {
    const { data } = await getMeditationTagList();
    allTags.value = data.items;
  } catch (error) {
    console.error("获取标签列表失败:", error);
  }
};

// 加载课程详情（编辑模式）
const loadCourseDetail = async () => {
  if (!isEdit) return;

  loading.value = true;
  try {
    const { data } = await getMeditationContentDetail(courseId);
    Object.assign(courseForm, {
      title: data.title,
      description: data.description,
      type: data.type,
      sub_type: data.sub_type,
      cover_url: data.cover_url || "",
      duration: data.duration,
      tag_ids: data.tags.map(tag => parseInt(tag.id)),
      status: data.status
    });
  } catch (error) {
    ElMessage.error("获取课程详情失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 保存课程
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit) {
      const updateData: UpdateMeditationContentParams = {
        title: courseForm.title,
        description: courseForm.description,
        type: courseForm.type,
        cover_url: courseForm.cover_url,
        tag_ids: courseForm.tag_ids,
        status: courseForm.status
      };
      await updateMeditationContent(courseId, updateData);
      ElMessage.success("更新课程成功");
    } else {
      await createMeditationContent(courseForm);
      ElMessage.success("创建课程成功");
    }

    router.push("/meditation/course");
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit ? "更新课程失败" : "创建课程失败");
      console.error(error);
    }
  } finally {
    saving.value = false;
  }
};

onMounted(() => {
  loadTags();
  loadCourseDetail();
});
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
}

// 封面上传样式
.cover-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 178px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.cover-image {
  width: 178px;
  height: 100px;
  object-fit: cover;
  display: block;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--el-color-primary);
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
