import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 金币奖励类型定义
export type CoinReward = {
  coins: number;
};

// 等级名称列表项类型定义
export type LevelNameItem = {
  id: number;
  level: number;
  name: string;
  required_energy: number;
};

// 等级名称列表响应类型
export type LevelNamesResponse = {
  code: number;
  message: string;
  data: LevelNameItem[];
};

// 多肉等级数据类型定义
export type SucculentLevel = {
  id: string;
  level: number;
  name: string;
  icon: string;
  required_energy: number;
  coin_reward: number;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at?: string | null;
};

// 分页信息类型定义
export type Pagination = {
  page: number;
  limit: number;
  total: number;
  pages: number;
};

// 多肉等级列表响应类型
export type SucculentLevelListResponse = {
  code: number;
  message: string;
  data: {
    list: SucculentLevel[];
    pagination: Pagination;
  };
};

// 多肉等级详情响应类型
export type SucculentLevelResponse = {
  code: number;
  message: string;
  data: SucculentLevel;
};

// 通用响应类型
export type CommonResponse = {
  code: number;
  message: string;
};

// 多肉等级列表查询参数
export type SucculentLevelListParams = {
  page?: number;
  limit?: number;
  search?: string;
  is_active?: boolean;
};

// 多肉等级创建参数
export type CreateSucculentLevelParams = {
  level: number;
  name: string;
  icon: string;
  required_energy: number;
  coin_reward: number;
  sort_order: number;
};

// 多肉等级更新参数
export type UpdateSucculentLevelParams = {
  name?: string;
  icon?: string;
  required_energy?: number;
  coin_reward?: number;
  sort_order?: number;
};

/** 获取多肉等级配置列表 */
export const getSucculentLevelList = (params?: SucculentLevelListParams) => {
  return http.request<SucculentLevelListResponse>(
    "get",
    baseUrlApi("admin/plants/levels"),
    { params }
  );
};

/** 获取指定等级配置详情 */
export const getSucculentLevelDetail = (id: string) => {
  return http.request<SucculentLevelResponse>(
    "get",
    baseUrlApi(`admin/plants/levels/${id}`)
  );
};

/** 创建新的等级配置 */
export const createSucculentLevel = (data: CreateSucculentLevelParams) => {
  return http.request<SucculentLevelResponse>(
    "post",
    baseUrlApi("admin/plants/levels"),
    { data }
  );
};

/** 更新等级配置 */
export const updateSucculentLevel = (
  id: string,
  data: UpdateSucculentLevelParams
) => {
  return http.request<SucculentLevelResponse>(
    "put",
    baseUrlApi(`admin/plants/levels/${id}`),
    { data }
  );
};

/** 切换等级配置状态（禁用/启用） */
export const toggleSucculentLevelStatus = (id: string) => {
  return http.request<CommonResponse>(
    "put",
    baseUrlApi(`admin/plants/levels/${id}/toggle`)
  );
};

/** 删除等级配置 */
export const deleteSucculentLevel = (id: string) => {
  return http.request<CommonResponse>(
    "delete",
    baseUrlApi(`admin/plants/levels/${id}`)
  );
};

/** 获取所有启用的等级配置（用于前端下拉选择） */
export const getActiveSucculentLevels = () => {
  return http.request<SucculentLevelListResponse>(
    "get",
    baseUrlApi("admin/plants/levels"),
    {
      params: {
        is_active: true,
        limit: 100
      }
    }
  );
};

/** 获取等级名称列表（用于多肉品种管理中成长阶段选择） */
export const getLevelNames = () => {
  return http.request<LevelNamesResponse>(
    "get",
    baseUrlApi("admin/plants/levels/names")
  );
};
