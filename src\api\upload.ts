import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 文件上传响应类型
export type UploadResponse = {
  code: number;
  message?: string;
  data: {
    url: string;
    filename: string;
    size: number;
    type: string;
  };
};

// 批量上传响应类型
export type MultipleUploadResponse = {
  code: number;
  message?: string;
  data: {
    urls: string[];
    files: {
      url: string;
      filename: string;
      size: number;
      type: string;
    }[];
  };
};

// 文件类型枚举
export type FileType = "image" | "audio" | "video" | "all";

// 单文件上传参数
export type UploadParams = {
  file: File;
  folder?: string;
  fileType?: FileType;
};

// 批量上传参数
export type MultipleUploadParams = {
  files: File[];
  folder?: string;
  fileType?: FileType;
};

/** 单文件上传 */
export const uploadFile = (params: UploadParams) => {
  const formData = new FormData();
  formData.append("file", params.file);
  
  if (params.folder) {
    formData.append("folder", params.folder);
  }
  
  if (params.fileType) {
    formData.append("fileType", params.fileType);
  }

  return http.request<UploadResponse>("post", baseUrlApi("oss/upload"), {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};

/** 批量文件上传 */
export const uploadMultipleFiles = (params: MultipleUploadParams) => {
  const formData = new FormData();
  
  params.files.forEach(file => {
    formData.append("files", file);
  });
  
  if (params.folder) {
    formData.append("folder", params.folder);
  }
  
  if (params.fileType) {
    formData.append("fileType", params.fileType);
  }

  return http.request<MultipleUploadResponse>("post", baseUrlApi("oss/upload/multiple"), {
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
};
