<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑章节' : '添加章节'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="chapterForm"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="章节标题" prop="title">
        <el-input
          v-model="chapterForm.title"
          placeholder="请输入章节标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="章节描述" prop="description">
        <el-input
          v-model="chapterForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入章节描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="封面图片">
        <el-input
          v-model="chapterForm.cover_url"
          placeholder="请输入封面图片URL"
        />
        <div v-if="chapterForm.cover_url" class="mt-2">
          <el-image
            :src="chapterForm.cover_url"
            style="width: 200px; height: 120px"
            fit="cover"
            :preview-src-list="[chapterForm.cover_url]"
          />
        </div>
      </el-form-item>

      <el-form-item label="音频文件">
        <el-input
          v-model="chapterForm.audio_url"
          placeholder="请输入音频文件URL"
        />
      </el-form-item>

      <el-form-item label="视频文件">
        <el-input
          v-model="chapterForm.video_url"
          placeholder="请输入视频文件URL"
        />
      </el-form-item>

      <el-form-item label="时长(min)" prop="duration">
        <el-input-number
          v-model="chapterForm.duration"
          :min="0"
          :step="60"
          placeholder="请输入时长"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="chapterForm.tag_ids"
          multiple
          placeholder="请选择标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in allTags"
            :key="tag.id"
            :label="tag.name"
            :value="parseInt(tag.id)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="排序">
        <el-input-number
          v-model="chapterForm.sort_order"
          :min="1"
          placeholder="请输入排序"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? "更新章节" : "添加章节" }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, type FormInstance } from "vue";
import { ElMessage } from "element-plus";
import {
  createCourseChapter,
  getCourseChapters,
  type CreateChapterParams,
  type CourseChapter,
  type MeditationTag
} from "@/api/meditation";

interface Props {
  modelValue: boolean;
  courseId: string;
  chapterId?: string;
  allTags: MeditationTag[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  chapterId: ""
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();

const isEdit = computed(() => !!props.chapterId);

const chapterForm = reactive<CreateChapterParams>({
  title: "",
  description: "",
  cover_url: "",
  audio_url: "",
  video_url: "",
  duration: 0,
  tag_ids: [],
  sort_order: 1
});

const formRules = {
  title: [{ required: true, message: "请输入章节标题", trigger: "blur" }],
  description: [{ required: true, message: "请输入章节描述", trigger: "blur" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }]
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      resetForm();
      if (isEdit.value) {
        loadChapterDetail();
      } else {
        // 新增时设置默认排序
        loadChapterCount();
      }
    }
  },
  { immediate: true }
);

// 监听内部visible状态
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 加载章节数量（用于设置默认排序）
const loadChapterCount = async () => {
  try {
    const { data } = await getCourseChapters(props.courseId);
    chapterForm.sort_order = data.chapters.length + 1;
  } catch (error) {
    console.error("获取章节数量失败:", error);
  }
};

// 加载章节详情（编辑模式）
const loadChapterDetail = async () => {
  if (!props.chapterId) return;

  loading.value = true;
  try {
    const { data } = await getCourseChapters(props.courseId);
    const chapter = data.chapters.find(c => c.id === props.chapterId);
    if (chapter) {
      Object.assign(chapterForm, {
        title: chapter.title,
        description: chapter.description,
        cover_url: chapter.cover_url || "",
        audio_url: chapter.audio_url || "",
        video_url: chapter.video_url || "",
        duration: chapter.duration,
        tag_ids: chapter.tags.map(tag => parseInt(tag.id)),
        sort_order: chapter.sort_order
      });
    }
  } catch (error) {
    ElMessage.error("获取章节详情失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 保存章节
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit.value) {
      // TODO: 实现编辑章节接口
      ElMessage.info("编辑章节功能待后端接口支持");
    } else {
      await createCourseChapter(props.courseId, chapterForm);
      ElMessage.success("添加章节成功");
    }

    handleClose();
    emit("success");
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? "更新章节失败" : "添加章节失败");
      console.error(error);
    }
  } finally {
    saving.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(chapterForm, {
    title: "",
    description: "",
    cover_url: "",
    audio_url: "",
    video_url: "",
    duration: 0,
    tag_ids: [],
    sort_order: 1
  });
};
</script>
