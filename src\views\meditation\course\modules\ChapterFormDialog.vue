<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑章节' : '添加章节'"
    width="600px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="chapterForm"
      :rules="formRules"
      label-width="100px"
      v-loading="loading"
    >
      <el-form-item label="章节标题" prop="title">
        <el-input
          v-model="chapterForm.title"
          placeholder="请输入章节标题"
          maxlength="100"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="章节描述" prop="description">
        <el-input
          v-model="chapterForm.description"
          type="textarea"
          :rows="3"
          placeholder="请输入章节描述"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="封面图片">
        <el-upload
          ref="coverUploadRef"
          class="cover-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="{ folder: 'meditation/chapters/covers', fileType: 'image' }"
          :show-file-list="false"
          :on-success="handleCoverSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeCoverUpload"
          :loading="coverUploading"
        >
          <img
            v-if="chapterForm.cover_url"
            :src="chapterForm.cover_url"
            class="cover-image"
          />
          <el-icon v-else class="cover-uploader-icon">
            <Plus />
          </el-icon>
          <div v-if="coverUploading" class="upload-loading">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <div>上传中...</div>
          </div>
        </el-upload>
        <div class="upload-tip">
          支持 jpg/jpeg/png/gif/webp 格式，大小不超过 5MB
        </div>
      </el-form-item>

      <el-form-item label="音频文件">
        <el-upload
          ref="audioUploadRef"
          class="audio-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="{ folder: 'meditation/chapters/audios', fileType: 'audio' }"
          :show-file-list="false"
          :on-success="handleAudioSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeAudioUpload"
          :loading="audioUploading"
        >
          <el-button type="primary" :loading="audioUploading">
            <el-icon><Upload /></el-icon>
            {{ chapterForm.audio_url ? "重新上传音频" : "上传音频文件" }}
          </el-button>
        </el-upload>
        <div v-if="chapterForm.audio_url" class="uploaded-file">
          <el-icon><Headphone /></el-icon>
          <span class="file-name">{{
            getFileName(chapterForm.audio_url)
          }}</span>
          <el-button type="danger" size="small" text @click="removeAudio">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        <div class="upload-tip">
          支持 mp3/wav/ogg/aac/m4a/flac 格式，大小不超过 50MB
        </div>
      </el-form-item>

      <el-form-item label="视频文件">
        <el-upload
          ref="videoUploadRef"
          class="video-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="{ folder: 'meditation/chapters/videos', fileType: 'all' }"
          :show-file-list="false"
          :on-success="handleVideoSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeVideoUpload"
          :loading="videoUploading"
        >
          <el-button type="primary" :loading="videoUploading">
            <el-icon><VideoCamera /></el-icon>
            {{ chapterForm.video_url ? "重新上传视频" : "上传视频文件" }}
          </el-button>
        </el-upload>
        <div v-if="chapterForm.video_url" class="uploaded-file">
          <el-icon><VideoCamera /></el-icon>
          <span class="file-name">{{
            getFileName(chapterForm.video_url)
          }}</span>
          <el-button type="danger" size="small" text @click="removeVideo">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        <div class="upload-tip">支持常见视频格式，大小不超过 100MB</div>
      </el-form-item>

      <el-form-item label="时长(min)" prop="duration">
        <el-input-number
          v-model="chapterForm.duration"
          :min="0"
          :step="60"
          placeholder="请输入时长"
          style="width: 100%"
        />
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="chapterForm.tag_ids"
          multiple
          placeholder="请选择标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in allTags"
            :key="tag.id"
            :label="tag.name"
            :value="parseInt(tag.id)"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="排序">
        <el-input-number
          v-model="chapterForm.sort_order"
          :min="1"
          placeholder="请输入排序"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave" :loading="saving">
        {{ isEdit ? "更新章节" : "添加章节" }}
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, type FormInstance } from "vue";
import { ElMessage, type UploadProps } from "element-plus";
import {
  Plus,
  Loading,
  Upload,
  Headphone,
  Delete,
  VideoCamera
} from "@element-plus/icons-vue";
import {
  createCourseChapter,
  getCourseChapters,
  type CreateChapterParams,
  type CourseChapter,
  type MeditationTag
} from "@/api/meditation";
import { type UploadResponse } from "@/api/upload";
import { getToken } from "@/utils/auth";

interface Props {
  modelValue: boolean;
  courseId: string;
  chapterId?: string;
  allTags: MeditationTag[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  chapterId: ""
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const saving = ref(false);
const formRef = ref<FormInstance>();

// 上传相关状态
const coverUploading = ref(false);
const audioUploading = ref(false);
const videoUploading = ref(false);

const isEdit = computed(() => !!props.chapterId);

// 上传配置
const uploadAction = computed(() => {
  return import.meta.env.VITE_BASE_API + "/api/oss/upload";
});

const uploadHeaders = computed(() => {
  const token = getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const chapterForm = reactive<CreateChapterParams>({
  title: "",
  description: "",
  cover_url: "",
  audio_url: "",
  video_url: "",
  duration: 0,
  tag_ids: [],
  sort_order: 1
});

const formRules = {
  title: [{ required: true, message: "请输入章节标题", trigger: "blur" }],
  description: [{ required: true, message: "请输入章节描述", trigger: "blur" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }]
};

// 文件上传相关方法
const getFileName = (url: string) => {
  if (!url) return "";
  return url.split("/").pop() || "";
};

// 封面图片上传
const beforeCoverUpload: UploadProps["beforeUpload"] = file => {
  const isImage = file.type.startsWith("image/");
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt5M) {
    ElMessage.error("图片大小不能超过 5MB!");
    return false;
  }
  coverUploading.value = true;
  return true;
};

const handleCoverSuccess = (response: UploadResponse) => {
  coverUploading.value = false;
  if (response.code === 200) {
    chapterForm.cover_url = response.data.url;
    ElMessage.success("封面上传成功");
  } else {
    ElMessage.error(response.message || "封面上传失败");
  }
};

// 音频文件上传
const beforeAudioUpload: UploadProps["beforeUpload"] = file => {
  const audioTypes = [
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
    "audio/aac",
    "audio/m4a",
    "audio/flac"
  ];
  const isAudio =
    audioTypes.includes(file.type) ||
    file.name.match(/\.(mp3|wav|ogg|aac|m4a|flac)$/i);
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isAudio) {
    ElMessage.error("只能上传音频文件!");
    return false;
  }
  if (!isLt50M) {
    ElMessage.error("音频文件大小不能超过 50MB!");
    return false;
  }
  audioUploading.value = true;
  return true;
};

const handleAudioSuccess = (response: UploadResponse) => {
  audioUploading.value = false;
  if (response.code === 200) {
    chapterForm.audio_url = response.data.url;
    ElMessage.success("音频上传成功");
  } else {
    ElMessage.error(response.message || "音频上传失败");
  }
};

const removeAudio = () => {
  chapterForm.audio_url = "";
};

// 视频文件上传
const beforeVideoUpload: UploadProps["beforeUpload"] = file => {
  const videoTypes = [
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/flv",
    "video/webm"
  ];
  const isVideo =
    videoTypes.includes(file.type) ||
    file.name.match(/\.(mp4|avi|mov|wmv|flv|webm)$/i);
  const isLt100M = file.size / 1024 / 1024 < 100;

  if (!isVideo) {
    ElMessage.error("只能上传视频文件!");
    return false;
  }
  if (!isLt100M) {
    ElMessage.error("视频文件大小不能超过 100MB!");
    return false;
  }
  videoUploading.value = true;
  return true;
};

const handleVideoSuccess = (response: UploadResponse) => {
  videoUploading.value = false;
  if (response.code === 200) {
    chapterForm.video_url = response.data.url;
    ElMessage.success("视频上传成功");
  } else {
    ElMessage.error(response.message || "视频上传失败");
  }
};

const removeVideo = () => {
  chapterForm.video_url = "";
};

// 通用上传错误处理
const handleUploadError = (error: any) => {
  coverUploading.value = false;
  audioUploading.value = false;
  videoUploading.value = false;
  console.error("上传失败:", error);
  ElMessage.error("文件上传失败，请重试");
};

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal) {
      resetForm();
      if (isEdit.value) {
        loadChapterDetail();
      } else {
        // 新增时设置默认排序
        loadChapterCount();
      }
    }
  },
  { immediate: true }
);

// 监听内部visible状态
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 加载章节数量（用于设置默认排序）
const loadChapterCount = async () => {
  try {
    const { data } = await getCourseChapters(props.courseId);
    chapterForm.sort_order = data.chapters.length + 1;
  } catch (error) {
    console.error("获取章节数量失败:", error);
  }
};

// 加载章节详情（编辑模式）
const loadChapterDetail = async () => {
  if (!props.chapterId) return;

  loading.value = true;
  try {
    const { data } = await getCourseChapters(props.courseId);
    const chapter = data.chapters.find(c => c.id === props.chapterId);
    if (chapter) {
      Object.assign(chapterForm, {
        title: chapter.title,
        description: chapter.description,
        cover_url: chapter.cover_url || "",
        audio_url: chapter.audio_url || "",
        video_url: chapter.video_url || "",
        duration: chapter.duration,
        tag_ids: chapter.tags.map(tag => parseInt(tag.id)),
        sort_order: chapter.sort_order
      });
    }
  } catch (error) {
    ElMessage.error("获取章节详情失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 保存章节
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit.value) {
      // TODO: 实现编辑章节接口
      ElMessage.info("编辑章节功能待后端接口支持");
    } else {
      await createCourseChapter(props.courseId, chapterForm);
      ElMessage.success("添加章节成功");
    }

    handleClose();
    emit("success");
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? "更新章节失败" : "添加章节失败");
      console.error(error);
    }
  } finally {
    saving.value = false;
  }
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  resetForm();
};

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  Object.assign(chapterForm, {
    title: "",
    description: "",
    cover_url: "",
    audio_url: "",
    video_url: "",
    duration: 0,
    tag_ids: [],
    sort_order: 1
  });
};
</script>

<style scoped lang="scss">
// 封面上传样式
.cover-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 178px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.cover-image {
  width: 178px;
  height: 100px;
  object-fit: cover;
  display: block;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--el-color-primary);
}

// 音频和视频上传样式
.audio-uploader,
.video-uploader {
  .el-button {
    width: 100%;
  }
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px;
  background: var(--el-fill-color-light);
  border-radius: 4px;

  .file-name {
    flex: 1;
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
