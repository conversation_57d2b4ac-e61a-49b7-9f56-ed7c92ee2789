<template>
  <el-dialog
    v-model="visible"
    title="章节管理"
    width="1200px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <!-- 课程信息 -->
    <el-card class="mb-4" v-if="courseInfo">
      <template #header>
        <span class="text-lg font-semibold">课程信息</span>
      </template>
      <div>
        <h3 class="text-xl mb-2">{{ courseInfo.title }}</h3>
        <p class="text-gray-600">{{ courseInfo.description }}</p>
      </div>
    </el-card>

    <!-- 章节管理工具栏 -->
    <div class="flex justify-between items-center mb-4">
      <span class="text-lg font-semibold">章节列表</span>
      <div class="space-x-2">
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAddChapter"
        >
          添加章节
        </el-button>
        <el-button
          :icon="useRenderIcon('ep:sort')"
          @click="handleReorderChapters"
          :disabled="!chapters.length"
        >
          调整顺序
        </el-button>
        <el-button :icon="useRenderIcon('ep:refresh')" @click="loadChapters">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 章节列表 -->
    <el-table
      :data="chapters"
      v-loading="loading"
      style="width: 100%"
      :header-cell-style="{
        background: 'var(--el-fill-color-light)',
        color: 'var(--el-text-color-primary)'
      }"
    >
      <el-table-column label="排序" prop="sort_order" width="80" />
      <el-table-column label="封面" width="100">
        <template #default="{ row }">
          <el-image
            v-if="row.cover_url"
            :src="row.cover_url"
            style="width: 60px; height: 60px"
            fit="cover"
            :preview-src-list="[row.cover_url]"
          />
          <span v-else class="text-gray-400">无封面</span>
        </template>
      </el-table-column>
      <el-table-column label="标题" prop="title" min-width="200" />
      <el-table-column label="描述" prop="description" min-width="200">
        <template #default="{ row }">
          <el-tooltip :content="row.description" placement="top">
            <span class="truncate max-w-[200px] block">
              {{ row.description }}
            </span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="时长" width="100">
        <template #default="{ row }">
          <span>{{ formatDuration(row.duration) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标签" min-width="150">
        <template #default="{ row }">
          <div class="flex flex-wrap gap-1">
            <el-tag v-for="tag in row.tags" :key="tag.id" size="small">
              {{ tag.name }}
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="收藏数" prop="favorite_count" width="100" />
      <el-table-column label="推荐" width="80">
        <template #default="{ row }">
          <el-tag :type="row.is_recommended ? 'success' : 'info'" size="small">
            {{ row.is_recommended ? "是" : "否" }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" width="80">
        <template #default="{ row }">
          <el-tag :type="getStatusTag(row.status).type" size="small">
            {{ getStatusTag(row.status).text }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="150">
        <template #default="{ row }">
          <el-button
            link
            type="primary"
            size="small"
            :icon="useRenderIcon('ep:edit')"
            @click="handleEditChapter(row)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            size="small"
            :icon="useRenderIcon('ep:delete')"
            @click="handleDeleteChapter(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>

    <!-- 添加/编辑章节对话框 -->
    <ChapterFormDialog
      v-model="chapterDialogVisible"
      :course-id="courseId"
      :chapter-id="editingChapterId"
      :all-tags="allTags"
      @success="loadChapters"
    />

    <!-- 调整顺序对话框 -->
    <ChapterReorderDialog
      v-model="reorderDialogVisible"
      :course-id="courseId"
      :chapters="chapters"
      @success="loadChapters"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import {
  getCourseChapters,
  deleteCourseChapter,
  type CourseChapter,
  type MeditationTag
} from "@/api/meditation";
import ChapterFormDialog from "./ChapterFormDialog.vue";
import ChapterReorderDialog from "./ChapterReorderDialog.vue";

interface Props {
  modelValue: boolean;
  courseId: string;
  allTags: MeditationTag[];
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
});

const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const chapterDialogVisible = ref(false);
const reorderDialogVisible = ref(false);
const editingChapterId = ref("");

const courseInfo = ref<{
  id: string;
  title: string;
  description: string;
} | null>(null);

const chapters = ref<CourseChapter[]>([]);

// 监听弹窗显示状态
watch(
  () => props.modelValue,
  newVal => {
    visible.value = newVal;
    if (newVal && props.courseId) {
      loadChapters();
    }
  },
  { immediate: true }
);

// 监听内部visible状态
watch(visible, newVal => {
  emit("update:modelValue", newVal);
});

// 加载章节列表
const loadChapters = async () => {
  if (!props.courseId) return;

  loading.value = true;
  try {
    const { data } = await getCourseChapters(props.courseId);
    courseInfo.value = data.course;
    chapters.value = data.chapters.sort((a, b) => a.sort_order - b.sort_order);
  } catch (error) {
    ElMessage.error("获取章节列表失败");
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 添加章节
const handleAddChapter = () => {
  editingChapterId.value = "";
  chapterDialogVisible.value = true;
};

// 编辑章节
const handleEditChapter = (chapter: CourseChapter) => {
  editingChapterId.value = chapter.id;
  chapterDialogVisible.value = true;
};

// 删除章节
const handleDeleteChapter = async (chapter: CourseChapter) => {
  try {
    await ElMessageBox.confirm(`确定要删除章节 ${chapter.title} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    await deleteCourseChapter(props.courseId, chapter.id);
    ElMessage.success("删除章节成功");
    loadChapters();
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("删除章节失败");
      console.error(error);
    }
  }
};

// 调整章节顺序
const handleReorderChapters = () => {
  reorderDialogVisible.value = true;
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
};

// 格式化时长
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return hours > 0 ? `${hours}h${mins}min` : `${mins}min`;
};

// 获取状态标签
const getStatusTag = (status: string) => {
  const statusMap = {
    draft: { type: "info", text: "草稿" },
    published: { type: "success", text: "已上架" },
    unpublished: { type: "warning", text: "已下架" },
    archived: { type: "warning", text: "已归档" }
  };
  return statusMap[status] || { type: "info", text: "未知" };
};
</script>
