<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑冥想内容' : '新增冥想内容'"
    width="800px"
    draggable
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
      label-position="right"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标题" prop="title">
            <el-input
              v-model="formData.title"
              placeholder="请输入内容标题"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择类型">
              <el-option label="冥想" value="meditation" />
              <el-option label="声音" value="sound" />
              <el-option label="睡眠" value="sleep" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="子类型" prop="sub_type">
            <el-select
              v-model="formData.sub_type"
              placeholder="请选择子类型"
              clearable
            >
              <el-option label="课程" value="course" />
              <el-option label="单节" value="single" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="时长(秒)" prop="duration">
            <el-input-number
              v-model="formData.duration"
              :min="0"
              :max="7200"
              placeholder="请输入时长"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态">
              <el-option label="已上架" value="published" />
              <el-option label="已下架" value="unpublished" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标签" prop="tag_ids">
            <el-select
              v-model="formData.tag_ids"
              multiple
              placeholder="请选择标签"
              style="width: 100%"
            >
              <el-option
                v-for="tag in tagOptions"
                :key="tag.id"
                :label="tag.name"
                :value="parseInt(tag.id)"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入内容描述"
          maxlength="200"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="封面图片">
            <el-upload
              ref="coverUploadRef"
              class="cover-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ folder: 'meditation/covers', fileType: 'image' }"
              :show-file-list="false"
              :on-success="handleCoverSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeCoverUpload"
              :loading="coverUploading"
            >
              <img
                v-if="formData.cover_url"
                :src="formData.cover_url"
                class="cover-image"
              />
              <el-icon v-else class="cover-uploader-icon">
                <Plus />
              </el-icon>
              <div v-if="coverUploading" class="upload-loading">
                <el-icon class="is-loading">
                  <Loading />
                </el-icon>
                <div>上传中...</div>
              </div>
            </el-upload>
            <div class="upload-tip">
              支持 jpg/jpeg/png/gif/webp 格式，大小不超过 5MB
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="音频文件">
            <el-upload
              ref="audioUploadRef"
              class="audio-uploader"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="{ folder: 'meditation/audios', fileType: 'audio' }"
              :show-file-list="false"
              :on-success="handleAudioSuccess"
              :on-error="handleUploadError"
              :before-upload="beforeAudioUpload"
              :loading="audioUploading"
            >
              <el-button type="primary" :loading="audioUploading">
                <el-icon><Upload /></el-icon>
                {{ formData.audio_url ? "重新上传音频" : "上传音频文件" }}
              </el-button>
            </el-upload>
            <div v-if="formData.audio_url" class="uploaded-file">
              <el-icon><Headphone /></el-icon>
              <span class="file-name">{{
                getFileName(formData.audio_url)
              }}</span>
              <el-button type="danger" size="small" text @click="removeAudio">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            <div class="upload-tip">
              支持 mp3/wav/ogg/aac/m4a/flac 格式，大小不超过 50MB
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="视频文件">
        <el-upload
          ref="videoUploadRef"
          class="video-uploader"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="{ folder: 'meditation/videos', fileType: 'all' }"
          :show-file-list="false"
          :on-success="handleVideoSuccess"
          :on-error="handleUploadError"
          :before-upload="beforeVideoUpload"
          :loading="videoUploading"
        >
          <el-button type="primary" :loading="videoUploading">
            <el-icon><VideoCamera /></el-icon>
            {{ formData.video_url ? "重新上传视频" : "上传视频文件" }}
          </el-button>
        </el-upload>
        <div v-if="formData.video_url" class="uploaded-file">
          <el-icon><VideoCamera /></el-icon>
          <span class="file-name">{{ getFileName(formData.video_url) }}</span>
          <el-button type="danger" size="small" text @click="removeVideo">
            <el-icon><Delete /></el-icon>
          </el-button>
        </div>
        <div class="upload-tip">支持常见视频格式，大小不超过 100MB</div>
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, computed } from "vue";
import {
  ElMessage,
  type FormInstance,
  type FormRules,
  type UploadProps
} from "element-plus";
import {
  Plus,
  Loading,
  Upload,
  Headphone,
  Delete,
  VideoCamera
} from "@element-plus/icons-vue";
import {
  createMeditationContent,
  updateMeditationContent,
  getMeditationTagList,
  type MeditationContent,
  type CreateMeditationContentParams,
  type UpdateMeditationContentParams,
  type MeditationTag
} from "@/api/meditation";
import { uploadFile, type UploadResponse } from "@/api/upload";
import { getToken } from "@/utils/auth";

interface Props {
  modelValue: boolean;
  data?: MeditationContent;
}

interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "success"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const visible = ref(false);
const loading = ref(false);
const formRef = ref<FormInstance>();
const tagOptions = ref<MeditationTag[]>([]);

// 上传相关状态
const coverUploading = ref(false);
const audioUploading = ref(false);
const videoUploading = ref(false);

const isEdit = ref(false);

// 上传配置
const uploadAction = computed(() => {
  return import.meta.env.VITE_BASE_API + "/api/oss/upload";
});

const uploadHeaders = computed(() => {
  const token = getToken();
  return token ? { Authorization: `Bearer ${token}` } : {};
});

const formData = reactive<CreateMeditationContentParams>({
  title: "",
  description: "",
  type: "meditation",
  sub_type: undefined,
  duration: 0,
  tag_ids: [],
  status: "unpublished",
  cover_url: "",
  audio_url: "",
  video_url: ""
});

const rules: FormRules = {
  title: [{ required: true, message: "请输入标题", trigger: "blur" }],
  type: [{ required: true, message: "请选择类型", trigger: "change" }],
  duration: [{ required: true, message: "请输入时长", trigger: "blur" }],
  status: [{ required: true, message: "请选择状态", trigger: "change" }]
};

// 获取标签选项
const fetchTagOptions = async () => {
  try {
    const response = await getMeditationTagList({ limit: 100 });
    if (response.code === 200) {
      tagOptions.value = response.data.items;
    }
  } catch (error) {
    console.error("获取标签列表失败:", error);
  }
};

// 文件上传相关方法
const getFileName = (url: string) => {
  if (!url) return "";
  return url.split("/").pop() || "";
};

// 封面图片上传
const beforeCoverUpload: UploadProps["beforeUpload"] = file => {
  const isImage = file.type.startsWith("image/");
  const isLt5M = file.size / 1024 / 1024 < 5;

  if (!isImage) {
    ElMessage.error("只能上传图片文件!");
    return false;
  }
  if (!isLt5M) {
    ElMessage.error("图片大小不能超过 5MB!");
    return false;
  }
  coverUploading.value = true;
  return true;
};

const handleCoverSuccess = (response: UploadResponse) => {
  coverUploading.value = false;
  if (response.code === 200) {
    formData.cover_url = response.data.url;
    ElMessage.success("封面上传成功");
  } else {
    ElMessage.error(response.message || "封面上传失败");
  }
};

// 音频文件上传
const beforeAudioUpload: UploadProps["beforeUpload"] = file => {
  const audioTypes = [
    "audio/mpeg",
    "audio/wav",
    "audio/ogg",
    "audio/aac",
    "audio/m4a",
    "audio/flac"
  ];
  const isAudio =
    audioTypes.includes(file.type) ||
    file.name.match(/\.(mp3|wav|ogg|aac|m4a|flac)$/i);
  const isLt50M = file.size / 1024 / 1024 < 50;

  if (!isAudio) {
    ElMessage.error("只能上传音频文件!");
    return false;
  }
  if (!isLt50M) {
    ElMessage.error("音频文件大小不能超过 50MB!");
    return false;
  }
  audioUploading.value = true;
  return true;
};

const handleAudioSuccess = (response: UploadResponse) => {
  audioUploading.value = false;
  if (response.code === 200) {
    formData.audio_url = response.data.url;
    ElMessage.success("音频上传成功");
  } else {
    ElMessage.error(response.message || "音频上传失败");
  }
};

const removeAudio = () => {
  formData.audio_url = "";
};

// 视频文件上传
const beforeVideoUpload: UploadProps["beforeUpload"] = file => {
  const videoTypes = [
    "video/mp4",
    "video/avi",
    "video/mov",
    "video/wmv",
    "video/flv",
    "video/webm"
  ];
  const isVideo =
    videoTypes.includes(file.type) ||
    file.name.match(/\.(mp4|avi|mov|wmv|flv|webm)$/i);
  const isLt100M = file.size / 1024 / 1024 < 100;

  if (!isVideo) {
    ElMessage.error("只能上传视频文件!");
    return false;
  }
  if (!isLt100M) {
    ElMessage.error("视频文件大小不能超过 100MB!");
    return false;
  }
  videoUploading.value = true;
  return true;
};

const handleVideoSuccess = (response: UploadResponse) => {
  videoUploading.value = false;
  if (response.code === 200) {
    formData.video_url = response.data.url;
    ElMessage.success("视频上传成功");
  } else {
    ElMessage.error(response.message || "视频上传失败");
  }
};

const removeVideo = () => {
  formData.video_url = "";
};

// 通用上传错误处理
const handleUploadError = (error: any) => {
  coverUploading.value = false;
  audioUploading.value = false;
  videoUploading.value = false;
  console.error("上传失败:", error);
  ElMessage.error("文件上传失败，请重试");
};

const resetForm = () => {
  Object.assign(formData, {
    title: "",
    description: "",
    type: "meditation",
    sub_type: undefined,
    duration: 0,
    tag_ids: [],
    status: "unpublished",
    cover_url: "",
    audio_url: "",
    video_url: ""
  });
  formRef.value?.clearValidate();
};

const handleClose = () => {
  emit("update:modelValue", false);
  resetForm();
};

const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    if (isEdit.value && props.data) {
      const updateData: UpdateMeditationContentParams = { ...formData };
      const response = await updateMeditationContent(props.data.id, updateData);
      if (response.code === 200) {
        ElMessage.success("编辑成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.message || "编辑失败");
      }
    } else {
      const response = await createMeditationContent(formData);
      if (response.code === 200) {
        ElMessage.success("新增成功");
        emit("success");
        handleClose();
      } else {
        ElMessage.error(response.message || "新增失败");
      }
    }
  } catch (error) {
    console.error("提交失败:", error);
    ElMessage.error("操作失败");
  } finally {
    loading.value = false;
  }
};

watch(
  () => props.modelValue,
  val => {
    visible.value = val;
    if (val) {
      isEdit.value = !!props.data;
      if (props.data) {
        Object.assign(formData, {
          title: props.data.title,
          description: props.data.description,
          type: props.data.type,
          sub_type: props.data.sub_type,
          duration: props.data.duration,
          tag_ids: props.data.tags.map(tag => parseInt(tag.id)),
          status: props.data.status,
          cover_url: props.data.cover_url || "",
          audio_url: props.data.audio_url || "",
          video_url: props.data.video_url || ""
        });
      } else {
        resetForm();
      }
    }
  }
);

watch(visible, val => {
  emit("update:modelValue", val);
});

onMounted(() => {
  fetchTagOptions();
});
</script>

<style scoped lang="scss">
// 封面上传样式
.cover-uploader {
  :deep(.el-upload) {
    border: 1px dashed var(--el-border-color);
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);
    width: 178px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.cover-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

.cover-image {
  width: 178px;
  height: 100px;
  object-fit: cover;
  display: block;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: var(--el-color-primary);
}

// 音频和视频上传样式
.audio-uploader,
.video-uploader {
  .el-button {
    width: 100%;
  }
}

.uploaded-file {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  padding: 8px;
  background: var(--el-fill-color-light);
  border-radius: 4px;

  .file-name {
    flex: 1;
    font-size: 14px;
    color: var(--el-text-color-regular);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.upload-tip {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
  margin-top: 4px;
  line-height: 1.4;
}
</style>
