<script setup lang="ts">
import { ref, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { getGrowthRecordDetail, type GrowthRecord } from "@/api/growth-records";

defineOptions({
  name: "GrowthRecordDetailDialog"
});

interface Props {
  visible: boolean;
  recordId: string | number;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:visible": [value: boolean];
}>();

const loading = ref(false);
const detailData = ref<GrowthRecord | null>(null);

// 弹窗显示状态的计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit("update:visible", value)
});

// 植物成长记录弹窗相关
const plantGrowthDialogVisible = ref(false);
const currentPlantData = ref<any>(null);

// 获取用户详情
const fetchDetail = async () => {
  if (!props.recordId) return;

  try {
    loading.value = true;
    const response = await getGrowthRecordDetail(props.recordId.toString());
    if (response.code === 200) {
      detailData.value = response.data;
    } else {
      ElMessage.error(response.message || "获取详情失败");
    }
  } catch (error) {
    console.error("获取详情失败:", error);
    ElMessage.error("获取详情失败");
  } finally {
    loading.value = false;
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "-";
  return new Date(dateString).toLocaleString("zh-CN");
};

// 获取健康状态文本和颜色
const getHealthStatusInfo = (status: string) => {
  const statusMap = {
    healthy: { text: "健康", color: "success" },
    excellent: { text: "优秀", color: "primary" },
    unhealthy: { text: "不健康", color: "danger" }
  };
  return statusMap[status] || { text: "未知", color: "info" };
};

// 关闭弹窗
const handleClose = () => {
  emit("update:visible", false);
  detailData.value = null;
};

// 查看植物全部成长记录
const handleViewPlantGrowth = (plant: any) => {
  currentPlantData.value = plant;
  plantGrowthDialogVisible.value = true;
};

// 监听弹窗显示状态
watch(
  () => props.visible,
  newVal => {
    if (newVal && props.recordId) {
      fetchDetail();
    }
  },
  { immediate: true }
);
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="成长记录详情"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div v-loading="loading" class="detail-content">
      <div v-if="detailData" class="space-y-6">
        <!-- 用户基本信息 -->
        <div class="bg-white rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">用户信息</h3>
          <div class="flex items-center mb-4">
            <el-avatar :size="60" :src="detailData.avatar_url" class="mr-4" />
            <div>
              <div class="font-medium text-lg">
                {{ detailData.nickname }}
              </div>
              <div class="text-sm text-gray-500">ID: {{ detailData.id }}</div>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-x-8 gap-y-3">
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">冥想等级:</span>
              <span class="font-medium"
                >Lv.{{ detailData.meditation_level }}</span
              >
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">连续天数:</span>
              <span class="font-medium">{{ detailData.streak_days }}天</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">成长天数:</span>
              <span class="font-medium">{{ detailData.growth_days }}天</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">健康状态:</span>
              <el-tag
                :type="getHealthStatusInfo(detailData.health_status).color"
              >
                {{ getHealthStatusInfo(detailData.health_status).text }}
              </el-tag>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">注册时间:</span>
              <span class="text-sm">{{
                formatDate(detailData.created_at)
              }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-gray-600">最后照料时间:</span>
              <span class="text-sm">{{
                formatDate(detailData.last_care_time)
              }}</span>
            </div>
          </div>
        </div>

        <!-- 多肉植物信息 -->
        <div class="bg-white rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">多肉植物</h3>
          <div
            v-if="detailData.plants && detailData.plants.length > 0"
            class="space-y-4"
          >
            <div
              v-for="plant in detailData.plants"
              :key="plant.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center mb-3">
                <div
                  class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3"
                >
                  <span class="text-green-600 text-xl">🌱</span>
                </div>
                <div>
                  <div class="font-medium">{{ plant.species }}</div>
                  <div class="text-sm text-gray-500">
                    等级: {{ plant.level }}
                  </div>
                </div>
              </div>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div class="flex items-center space-x-2">
                  <span class="text-gray-600">能量值:</span>
                  <span class="font-medium text-orange-600">{{
                    plant.energy_value
                  }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span class="text-gray-600">创建时间:</span>
                  <span>{{ formatDate(plant.created_at).split(" ")[0] }}</span>
                </div>
              </div>

              <!-- 成长记录 -->
              <div v-if="plant.growth_records.length > 0" class="mt-3">
                <div class="flex justify-between items-center mb-2">
                  <div class="text-sm font-medium text-gray-700">
                    最近成长记录:
                  </div>
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleViewPlantGrowth(plant)"
                  >
                    查看全部
                  </el-button>
                </div>
                <div class="space-y-1">
                  <div
                    v-for="record in plant.growth_records.slice(0, 2)"
                    :key="record.id"
                    class="text-xs bg-gray-50 rounded p-2"
                  >
                    <div class="flex justify-between items-center">
                      <span
                        :class="
                          record.change_value > 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        "
                      >
                        {{ record.change_value > 0 ? "+" : ""
                        }}{{ record.change_value }}
                      </span>
                      <span class="text-gray-500">{{
                        formatDate(record.created_at).split(" ")[0]
                      }}</span>
                    </div>
                    <div class="text-gray-600 mt-1">{{ record.reason }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center text-gray-400 py-8">暂无多肉植物</div>
        </div>

        <!-- 多肉统计信息 -->
        <div class="bg-white rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">多肉统计</h3>
          <div class="grid grid-cols-3 gap-4 mb-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
              <div class="text-2xl font-bold text-blue-600">
                {{ detailData.total_energy || 0 }}
              </div>
              <div class="text-sm text-gray-600">总能量值</div>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
              <div class="text-2xl font-bold text-green-600">
                {{ detailData.plant_count || 0 }}
              </div>
              <div class="text-sm text-gray-600">多肉数量</div>
            </div>
            <div class="text-center p-4 bg-purple-50 rounded-lg">
              <div class="text-2xl font-bold text-purple-600">
                {{
                  detailData.avg_plant_level
                    ? detailData.avg_plant_level.toFixed(1)
                    : "0.0"
                }}
              </div>
              <div class="text-sm text-gray-600">平均多肉等级</div>
            </div>
          </div>
          <div class="grid grid-cols-2 gap-4">
            <div class="text-center p-4 bg-orange-50 rounded-lg">
              <div class="text-2xl font-bold text-orange-600">
                {{ Math.max(...(detailData.plants?.map(p => p.level) || [0])) }}
              </div>
              <div class="text-sm text-gray-600">最高等级</div>
            </div>
            <div class="text-center p-4 bg-yellow-50 rounded-lg">
              <div class="text-2xl font-bold text-yellow-600">
                {{
                  detailData.achievements ? detailData.achievements.length : 0
                }}
              </div>
              <div class="text-sm text-gray-600">获得成就</div>
            </div>
          </div>
        </div>

        <!-- 冥想统计信息 -->
        <div class="bg-white rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 mb-4">冥想统计</h3>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-indigo-50 rounded-lg">
              <div class="text-2xl font-bold text-indigo-600">
                {{
                  Math.floor((detailData.total_meditation_duration || 0) / 60)
                }}
              </div>
              <div class="text-sm text-gray-600">总冥想时长(分钟)</div>
            </div>
            <div class="text-center p-4 bg-emerald-50 rounded-lg">
              <div class="text-2xl font-bold text-emerald-600">
                {{ detailData.total_energy_gained || 0 }}
              </div>
              <div class="text-sm text-gray-600">总获得能量</div>
            </div>
            <div class="text-center p-4 bg-rose-50 rounded-lg">
              <div class="text-2xl font-bold text-rose-600">
                {{
                  detailData.meditation_stats?.reduce(
                    (sum, stat) => sum + stat.tasks_completed,
                    0
                  ) || 0
                }}
              </div>
              <div class="text-sm text-gray-600">完成任务数</div>
            </div>
          </div>
        </div>

        <!-- 成就列表 -->
        <div
          v-if="detailData.achievements && detailData.achievements.length > 0"
          class="bg-white rounded-lg p-4"
        >
          <h3 class="text-lg font-semibold text-gray-800 mb-4">获得成就</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="achievement in detailData.achievements"
              :key="achievement.id"
              class="border rounded-lg p-4"
            >
              <div class="flex items-center mb-2">
                <div
                  class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3"
                >
                  <span class="text-yellow-600">🏆</span>
                </div>
                <div class="font-medium">
                  {{ achievement.achievement_name }}
                </div>
              </div>
              <div class="text-sm text-gray-600 mb-2">
                {{ achievement.description }}
              </div>
              <div class="text-xs text-gray-500">
                获得时间: {{ formatDate(achievement.unlocked_at) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!detailData && !loading" class="text-center py-12">
        <div class="text-gray-400 text-lg mb-4">未找到用户信息</div>
      </div>
    </div>
  </el-dialog>

  <!-- 植物成长记录详情弹窗 -->
  <el-dialog
    v-model="plantGrowthDialogVisible"
    :title="`${currentPlantData?.species || '植物'} - 成长记录`"
    width="50%"
    destroy-on-close
  >
    <div v-if="currentPlantData" class="space-y-4">
      <!-- 植物基本信息 -->
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center mb-3">
          <div
            class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-3"
          >
            <span class="text-green-600 text-xl">🌱</span>
          </div>
          <div>
            <div class="font-medium text-lg">
              {{ currentPlantData.species }}
            </div>
            <div class="text-sm text-gray-500">
              等级: {{ currentPlantData.level }} | 能量:
              {{ currentPlantData.energy_value }}
            </div>
          </div>
        </div>
      </div>

      <!-- 成长记录列表 -->
      <div class="space-y-2">
        <h4 class="font-medium text-gray-800">全部成长记录</h4>
        <div class="max-h-96 overflow-y-auto space-y-2">
          <div
            v-for="record in currentPlantData.growth_records"
            :key="record.id"
            class="bg-white border rounded-lg p-3"
          >
            <div class="flex justify-between items-start mb-2">
              <span
                :class="
                  record.change_value > 0
                    ? 'text-green-600 font-medium'
                    : 'text-red-600 font-medium'
                "
              >
                {{ record.change_value > 0 ? "+" : ""
                }}{{ record.change_value }} 能量
              </span>
              <span class="text-sm text-gray-500">
                {{ formatDate(record.created_at) }}
              </span>
            </div>
            <div class="text-gray-700">{{ record.reason }}</div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped lang="scss">
.detail-content {
  padding: 0;
  max-height: 70vh;
  overflow-y: auto;
}
</style>
