<script setup lang="ts">
import { ref, reactive, onMounted, h } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { PureTableBar } from "@/components/RePureTableBar";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import DetailDialog from "./modules/DetailDialog.vue";
import EditDialog from "./modules/EditDialog.vue";
import {
  getSucculentLevelList,
  deleteSucculentLevel,
  toggleSucculentLevelStatus,
  type SucculentLevel
} from "@/api/succulent-levels";

defineOptions({
  name: "SucculentLevel"
});

const loading = ref(false);
const tableData = ref<SucculentLevel[]>([]);
const pagination = reactive({
  total: 0,
  pageSize: 10,
  currentPage: 1
});

// 搜索表单
const searchForm = reactive({
  search: "",
  is_active: undefined as boolean | undefined
});

const detailDialogVisible = ref(false);
const editDialogVisible = ref(false);
const currentRow = ref<SucculentLevel | null>(null);
const isEdit = ref(false);

// 表格列配置
const columns: TableColumnList = [
  {
    label: "等级",
    prop: "level",
    minWidth: 80
  },
  {
    label: "名称",
    prop: "name",
    minWidth: 120
  },
  {
    label: "图标",
    prop: "icon",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      return h("img", {
        src: row.icon,
        alt: row.name,
        style:
          "width: 32px; height: 32px; object-fit: cover; border-radius: 4px;"
      });
    }
  },
  {
    label: "所需能量",
    prop: "required_energy",
    minWidth: 100
  },
  {
    label: "金币奖励",
    prop: "coin_reward",
    minWidth: 100
  },
  {
    label: "状态",
    prop: "is_active",
    minWidth: 80,
    cellRenderer: ({ row }) => {
      return h(
        "el-tag",
        {
          type: row.is_active ? "success" : "danger",
          size: "small"
        },
        row.is_active ? "启用" : "禁用"
      );
    }
  },
  {
    label: "操作",
    fixed: "right",
    width: 240,
    slot: "operation"
  }
];

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const response = await getSucculentLevelList({
      page: pagination.currentPage,
      limit: pagination.pageSize,
      search: searchForm.search || undefined,
      is_active: searchForm.is_active
    });

    if (response.code === 200) {
      tableData.value = response.data.list;
      pagination.total = response.data.pagination.total;
    } else {
      ElMessage.error("获取多肉等级列表失败");
    }
  } catch (error) {
    console.error("获取多肉等级列表失败:", error);
    ElMessage.error("获取多肉等级列表失败");
  } finally {
    loading.value = false;
  }
};

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.currentPage = 1;
  loadData();
};

const handleCurrentChange = (val: number) => {
  pagination.currentPage = val;
  loadData();
};

// 搜索
const onSearch = () => {
  pagination.currentPage = 1;
  loadData();
};

// 重置搜索
const resetForm = () => {
  searchForm.search = "";
  searchForm.is_active = undefined;
  onSearch();
};

// 新增等级
const handleAdd = () => {
  isEdit.value = false;
  currentRow.value = null;
  editDialogVisible.value = true;
};

// 查看详情
const handleDetail = (row: SucculentLevel) => {
  currentRow.value = row;
  detailDialogVisible.value = true;
};

// 编辑等级
const handleEdit = (row: SucculentLevel) => {
  isEdit.value = true;
  currentRow.value = row;
  editDialogVisible.value = true;
};

// 删除等级
const handleDelete = async (row: SucculentLevel) => {
  try {
    await ElMessageBox.confirm(`确定要删除等级 ${row.name} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await deleteSucculentLevel(row.id);
    if (response.code === 200) {
      ElMessage.success("删除成功");
      loadData();
    } else {
      ElMessage.error("删除失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("删除等级失败:", error);
      ElMessage.error("删除失败");
    }
  }
};

// 切换状态
const handleToggleStatus = async (row: SucculentLevel) => {
  const action = row.is_active ? "禁用" : "启用";
  try {
    await ElMessageBox.confirm(`确定要${action}等级 ${row.name} 吗？`, "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning"
    });

    const response = await toggleSucculentLevelStatus(row.id);
    if (response.code === 200) {
      ElMessage.success(`${action}成功`);
      loadData();
    } else {
      ElMessage.error(`${action}失败`);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("切换状态失败:", error);
      ElMessage.error(`${action}失败`);
    }
  }
};

// 处理编辑对话框的保存事件
const handleEditSave = () => {
  loadData();
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <div class="main">
    <!-- 搜索表单 -->
    <el-form
      ref="formRef"
      :inline="true"
      :model="searchForm"
      class="search-form bg-bg_color w-[99/100] pl-8 pt-[12px]"
    >
      <el-form-item label="搜索关键词：" prop="search">
        <el-input
          v-model="searchForm.search"
          placeholder="请输入等级名称"
          clearable
          class="!w-[200px]"
          @keyup.enter="onSearch"
        />
      </el-form-item>
      <el-form-item label="状态：" prop="is_active">
        <el-select
          v-model="searchForm.is_active"
          placeholder="请选择状态"
          clearable
          class="!w-[180px]"
        >
          <el-option label="启用" :value="true" />
          <el-option label="禁用" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          :icon="useRenderIcon('ri:search-line')"
          :loading="loading"
          @click="onSearch"
        >
          搜索
        </el-button>
        <el-button :icon="useRenderIcon('ri:refresh-line')" @click="resetForm">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- 表格工具栏 -->
    <PureTableBar title="多肉等级系统" :columns="columns" @refresh="onSearch">
      <template #buttons>
        <el-button
          type="primary"
          :icon="useRenderIcon('ep:plus')"
          @click="handleAdd"
        >
          新增等级
        </el-button>
      </template>
      <template v-slot="{ size, dynamicColumns }">
        <!-- 表格 -->
        <pure-table
          adaptive
          :adaptiveConfig="{ offsetBottom: 108 }"
          align-whole="center"
          table-layout="auto"
          :loading="loading"
          :size="size"
          :data="tableData"
          :columns="dynamicColumns"
          :pagination="pagination"
          :paginationSmall="size === 'small'"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="handleSizeChange"
          @page-current-change="handleCurrentChange"
        >
          <template #operation="{ row, size }">
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:view')"
              @click="handleDetail(row)"
            >
              详情
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="primary"
              :size="size"
              :icon="useRenderIcon('ep:edit')"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              class="reset-margin"
              link
              :type="row.is_active ? 'warning' : 'success'"
              :size="size"
              :icon="useRenderIcon(row.is_active ? 'ep:lock' : 'ep:unlock')"
              @click="handleToggleStatus(row)"
            >
              {{ row.is_active ? "禁用" : "启用" }}
            </el-button>
            <el-button
              class="reset-margin"
              link
              type="danger"
              :size="size"
              :icon="useRenderIcon('ep:delete')"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </pure-table>
      </template>
    </PureTableBar>

    <!-- 详情对话框 -->
    <DetailDialog v-model="detailDialogVisible" :data="currentRow" />

    <!-- 新增/编辑对话框 -->
    <EditDialog
      v-model="editDialogVisible"
      :data="currentRow"
      :is-edit="isEdit"
      @save="handleEditSave"
    />
  </div>
</template>

<style scoped lang="scss">
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}
</style>
